import { valibotResolver } from "@hookform/resolvers/valibot";
import type { EditTeachingPlanSectionPayload } from "@sisva/api/ai";
// Import AI hooks
import {
  useEditTeachingPlanSection,
  useGenerateTeachingPlan,
  useGenerateTeachingPlanSection,
} from "@sisva/hooks/query/classroom/useAi";
import { useTasks, useCreateTask } from "@sisva/hooks/query/classroom/useTasks";
import { useTeachingMaterials } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import {
  useCreateTeachingPlan,
  useTeachingPlan,
  useUpdateTeachingPlan,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useExamsByTeachingPlanId } from "@sisva/hooks/query/exam/useExams";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { createRedirectUrl } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import {
  Download01,
  Edit03,
  File06,
  Plus,
  Stars02, // AI icon
  Trash03,
} from "@untitled-ui/icons-react";
import { Button, Form, Input, Modal, Table, Tag, theme } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import { array, nonEmpty, number, object, pipe, string } from "valibot";
// Import hooks for class information
import { useStudentGroupByClassId } from "@sisva/hooks/query/academic/useStudentGroups";
import { usePeriodCurriculums } from "@sisva/hooks/query/academic/usePeriods";
import { useSubjectByClassId } from "@sisva/hooks/query/academic/useSubjects";

import { env } from "#/env";
import { CreateTaskButton } from "#/routes/(main)/-components/CreateTaskButton";
import { CreateTeachingMaterialButton } from "#/routes/(main)/-components/CreateTeachingMaterialButton";
import CrepeEditor from "#/routes/(main)/-components/CrepeEditor";

import { TeachingMaterialsSelectorButton } from "./TeachingMaterialsSelectorButton";

export default function TeachingPlanForm({
  class_id,
  teaching_plan_id,
  onSuccess,
}: {
  class_id: number;
  teaching_plan_id?: number;
  onSuccess?: () => void;
}) {
  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);
  const notification = useNotificationAPI();
  const token = theme.useToken().token;
  const currentUser = useCurrentUser();

  // Get class information for autofill
  const { data: studentGroup } = useStudentGroupByClassId(class_id);
  const { data: subject } = useSubjectByClassId(class_id);
  const { data: periodCurriculums = [] } = usePeriodCurriculums();

  // Find curriculum for this student group
  const curriculum = periodCurriculums.find(
    (pc) =>
      pc.period_id === studentGroup?.period_id &&
      pc.study_program_id === studentGroup?.study_program_id &&
      pc.grade === studentGroup?.grade
  );

  // Prepare autofill data
  const className = studentGroup?.name || "";
  const grade = studentGroup?.grade || "";
  const curriculumName = curriculum?.curriculum_name || "Kurikulum Merdeka";
  const subjectName = subject?.name || "";

  // AI Modal states
  const [isAIModalVisible, setIsAIModalVisible] = useState(false);
  const [aiModalType, setAiModalType] = useState<"generate" | "edit" | "generateSection" | null>(
    null
  );
  const [selectedSection, setSelectedSection] =
    useState<EditableByAISection | null>(null);
  const [aiInstructions, setAiInstructions] = useState("");
  const [aiSpecifications, setAiSpecifications] = useState("");
  const [aiTitle, setAiTitle] = useState("");
  const [editorRefreshKey, setEditorRefreshKey] = useState(0);

  const schema = object({
    class_id: number(),
    title: pipe(string(), nonEmpty("Judul harus diisi")),
    markdown: string(),
    teaching_materials: array(object({ id: number() })),
    tasks: array(object({ id: number() })),
    teaching_goal: string(),
    teaching_activity: string(),
    teaching_scoring: string(),
  });

  type EditableByAISection = Exclude<
    keyof typeof schema.entries,
    "class_id" | "tasks" | "teaching_materials" | "title"
  >;

  const sectionTypes: Record<
    EditableByAISection,
    EditTeachingPlanSectionPayload["sectionType"]
  > = {
    markdown: "description",
    teaching_goal: "teachingGoal",
    teaching_activity: "teachingActivity",
    teaching_scoring: "teachingScoring",
  };

  const {
    control,
    watch,
    setValue,
    formState: { errors },
    handleSubmit,
  } = useForm({
    values: {
      class_id: class_id,
      title: teachingPlan?.title ?? "",
      markdown: teachingPlan?.markdown ?? "",
      teaching_materials: teachingPlan?.teaching_materials ?? [],
      tasks: teachingPlan?.tasks ?? [],
      teaching_goal: teachingPlan?.teaching_goal ?? "",
      teaching_activity: teachingPlan?.teaching_activity ?? "",
      teaching_scoring: teachingPlan?.teaching_scoring ?? "",
    },
    resolver: valibotResolver(schema),
  });

  // AI Hooks
  const { mutate: generateTeachingPlan, isPending: isGeneratingPlan } =
    useGenerateTeachingPlan();

  const { mutate: editTeachingPlanSection, isPending: isEditingSection } =
    useEditTeachingPlanSection();

  const { mutate: generateTeachingPlanSection, isPending: isGeneratingSection } =
    useGenerateTeachingPlanSection();

  const { mutate: createTask } = useCreateTask();

  const getFileUrl = useGetFileUrl();

  const { mutate: createTeachingPlan } = useCreateTeachingPlan({
    onSuccess() {
      notification.success({ message: "Rencana pembelajaran berhasil dibuat" });
      onSuccess?.();
    },
    onError() {
      notification.error({ message: "Rencana Pembelajaran gagal dibuat" });
    },
  });

  const { mutate: updateTeachingPlan } = useUpdateTeachingPlan({
    onSuccess() {
      notification.success({
        message: "Rencana Pembelajaran berhasil perbarui",
      });
      onSuccess?.();
    },
    onError() {
      notification.error({ message: "Rencana Pembelajaran gagal perbarui" });
    },
  });

  const { data: teaching_materials = [] } = useTeachingMaterials();
  const { data: tasks = [] } = useTasks({ class_id, all: true });
  const { data: exams = [] } = useExamsByTeachingPlanId(teaching_plan_id);

  const form_teaching_materials = watch("teaching_materials") ?? [];
  const teachingMaterialIds = form_teaching_materials.map((item) => item.id);
  const filteredTeachingMaterials = teaching_materials.filter((item) =>
    teachingMaterialIds.includes(item.id)
  );

  const form_tasks = watch("tasks") ?? [];
  const taskIds = form_tasks.map((item) => item.id);
  const filteredTasks = tasks.filter((item) => taskIds.includes(item.id));

  // AI Helper Functions
  const handleGenerateFullPlan = () => {
    const title = watch("title");
    setAiTitle(title || ""); // Set current title as default
    setAiModalType("generate");
    setIsAIModalVisible(true);
  };

  const handleEditSection = (sectionType: typeof selectedSection) => {
    setSelectedSection(sectionType);
    setAiModalType("edit");
    setIsAIModalVisible(true);
  };

  const handleGenerateSection = (sectionType: typeof selectedSection) => {
    const title = watch("title");
    setAiTitle(title || ""); // Set current title as default
    setSelectedSection(sectionType);
    setAiModalType("generateSection");
    setIsAIModalVisible(true);
  };

  // Helper function to check if a section is empty
  const isSectionEmpty = (sectionType: EditableByAISection) => {
    const content = watch(sectionType);
    return !content || content.trim() === "";
  };

  // Helper function to handle AI action based on section content
  const handleAIAction = (sectionType: EditableByAISection) => {
    if (isSectionEmpty(sectionType)) {
      handleGenerateSection(sectionType);
    } else {
      handleEditSection(sectionType);
    }
  };

  const handleAIModalOk = () => {
    if (aiModalType === "generate") {
      if (!aiTitle.trim()) {
        notification.warning({
          message: "Judul diperlukan",
          description: "Silakan isi judul untuk menggunakan AI",
        });
        return;
      }

      generateTeachingPlan(
        {
          title: aiTitle,
          specifications: aiSpecifications || undefined,
        },
        {
          onSuccess: (data) => {
            setValue("markdown", data.description);
            setValue("teaching_goal", data.teachingGoal);
            setValue("teaching_activity", data.teachingActivity);
            setValue("teaching_scoring", data.teachingScoring);
            
            // Create tasks if they exist in the response
            if (data.tasks && data.tasks.length > 0) {
              const currentTasks = watch("tasks") || [];
              let createdTasksCount = 0;
              
              data.tasks.forEach((task) => {
                createTask(
                  {
                    name: task.name,
                    description: task.description,
                    class_id: class_id,
                    deadline: dayjs().add(7, 'day').format("DD/MM/YYYY h:mm A Z"), // Default 7 days from now, matching the expected format
                    attachment_file_uri: "",
                    allow_submission: true,
                    allow_overdue_submission: false,
                  },
                  {
                    onSuccess: (taskResult) => {
                      // Add the created task to the form
                      const updatedTasks = [...currentTasks, { id: taskResult.task_id }];
                      setValue("tasks", updatedTasks);
                      createdTasksCount++;
                      
                      // Show notification when all tasks are created
                      if (createdTasksCount === data.tasks.length) {
                        notification.success({
                          message: "Tugas berhasil dibuat",
                          description: `${data.tasks.length} tugas telah dibuat dan ditambahkan ke rencana pembelajaran.`,
                        });
                      }
                    },
                    onError: (error) => {
                      notification.error({
                        message: "Gagal membuat tugas",
                        description: `Gagal membuat tugas "${task.name}": ${error?.message || "Terjadi kesalahan"}`,
                      });
                    },
                  }
                );
              });
            }
            
            setEditorRefreshKey((prevKey) => prevKey + 1);
            notification.success({
              message: "Rencana pembelajaran berhasil dibuat dengan AI",
              description:
                "Semua field telah diisi otomatis. Anda dapat mengedit sesuai kebutuhan.",
            });
            setIsAIModalVisible(false);
            setAiInstructions("");
            setAiSpecifications("");
            setAiTitle("");
          },
          onError: (error) => {
            notification.error({
              message: "Gagal membuat rencana pembelajaran dengan AI",
              description:
                error?.message || "Terjadi kesalahan saat menggunakan AI",
            });
          },
        }
      );
    } else if (aiModalType === "edit" && selectedSection) {
      editTeachingPlanSection(
        {
          sectionType: sectionTypes[selectedSection],
          currentContent: watch(selectedSection) ?? "",
          instructions: aiInstructions,
        },
        {
          onSuccess: (data) => {
            if (selectedSection) {
              setValue(selectedSection, data);
            }
            setEditorRefreshKey((prevKey) => prevKey + 1);
            notification.success({
              message: "Berhasil mengedit dengan AI",
              description: "Konten telah diperbarui sesuai instruksi Anda",
            });
            
            setIsAIModalVisible(false);
            setAiInstructions("");
            setSelectedSection(null);
          },
          onError: (error) => {
            notification.error({
              message: "Gagal mengedit dengan AI",
              description:
                error?.message || "Terjadi kesalahan saat menggunakan AI",
            });
          },
        }
      );
    } else if (aiModalType === "generateSection" && selectedSection) {
      if (!aiTitle.trim()) {
        notification.warning({
          message: "Judul diperlukan",
          description: "Silakan isi judul untuk menggunakan AI",
        });
        return;
      }

      generateTeachingPlanSection(
        {
          sectionType: sectionTypes[selectedSection],
          title: aiTitle,
          specifications: aiSpecifications || undefined,
        },
        {
          onSuccess: (data) => {
            if (selectedSection) {
              setValue(selectedSection, data);
            }
            setEditorRefreshKey((prevKey) => prevKey + 1);
            notification.success({
              message: "Berhasil membuat bagian dengan AI",
              description: "Konten telah dibuat sesuai spesifikasi Anda",
            });
            
            setIsAIModalVisible(false);
            setAiInstructions("");
            setAiSpecifications("");
            setAiTitle("");
            setSelectedSection(null);
          },
          onError: (error) => {
            notification.error({
              message: "Gagal membuat bagian dengan AI",
              description:
                error?.message || "Terjadi kesalahan saat menggunakan AI",
            });
          },
        }
      );
    }
  };

  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setAiModalType(null);
    setSelectedSection(null);
    setAiInstructions("");
    setAiSpecifications("");
    setAiTitle("");
  };

  const columnsForTeachingMaterials: ColumnsType<
    (typeof filteredTeachingMaterials)[number]
  > = [
    {
      title: "Judul",
      dataIndex: "description",
      render: (value, record) => {
        return (
          <div className="flex items-center gap-1">
            <File06 width={16} />
            <div>{value}</div>
          </div>
        );
      },
    },
    {
      title: "Aksi",
      render: (_, record) => {
        return (
          <div className="flex gap-2 ">
            {record.attachment_file_uri && (
              <Button
                href={getFileUrl(record.attachment_file_uri)}
                variant="outlined"
                icon={<Download01 width={20} />}
              ></Button>
            )}
            <Button
              onClick={() => {
                setValue(
                  "teaching_materials",
                  form_teaching_materials.filter(
                    (item) => item.id !== record.id
                  )
                );
              }}
              variant="outlined"
              icon={<Trash03 width={20} />}
            ></Button>
            <CreateTeachingMaterialButton
              class_id={class_id}
              teaching_material_id={record.id}
              renderTrigger={(onClick) => {
                return (
                  <Button
                    onClick={onClick}
                    variant="outlined"
                    icon={<Edit03 width={20} />}
                  ></Button>
                );
              }}
            />
          </div>
        );
      },
      width: 100,
    },
  ];

  const columnsForTasks: ColumnsType<(typeof filteredTasks)[number]> = [
    {
      title: "Nama",
      dataIndex: "name",
      render: (value, record) => {
        return (
          <div className="flex items-center gap-1">
            <File06 width={16} />
            <div>{value}</div>
          </div>
        );
      },
    },
    {
      title: "Deskripsi",
      dataIndex: "description",
      render: (value, record) => {
        return (
          <div className="flex items-center gap-1">
            <div>{value}</div>
          </div>
        );
      },
    },
    {
      title: "Aksi",
      render: (_, record) => {
        return (
          <div className="flex gap-2 justify-end">
            {record.attachment_file_uri && (
              <Button
                href={getFileUrl(record.attachment_file_uri)}
                variant="outlined"
                icon={<Download01 width={20} />}
              ></Button>
            )}
            <Button
              onClick={() => {
                setValue(
                  "tasks",
                  form_tasks.filter((item) => item.id !== record.id)
                );
              }}
              variant="outlined"
              icon={<Trash03 width={20} />}
            ></Button>
            <CreateTaskButton
              class_id={class_id}
              task_id={record.id}
              renderTrigger={(onClick) => {
                return (
                  <Button
                    onClick={onClick}
                    variant="outlined"
                    icon={<Edit03 width={20} />}
                  ></Button>
                );
              }}
            />
          </div>
        );
      },
      width: 100,
    },
  ];

  const columnsForExams: ColumnsType<(typeof exams)[number]> = [
    {
      title: "Nama Ujian",
      minWidth: 150,
      dataIndex: "name",
    },
    {
      title: "Kelas",
      dataIndex: "class_ids",
      render(_, record) {
        return record.classes.map((class_) => (
          <Tag color={token.colorPrimaryText} key={class_.id}>
            {class_.student_group_name}
          </Tag>
        ));
      },
    },

    {
      key: "date",
      title: "Tanggal",
      dataIndex: "start_time",
      minWidth: 120,
      defaultSortOrder: "descend",
      render(_, record) {
        return dayjs(record.start_time, "DD/MM/YYYY h:mm A Z").format(
          "DD/MM/YYYY"
        );
      },
    },
    {
      key: "startTime",
      minWidth: 100,
      title: "Jam Mulai",
      dataIndex: "start_time",
      render(_, record) {
        return dayjs(record.start_time, "DD/MM/YYYY h:mm A Z").format("HH:MM");
      },
    },
    {
      minWidth: 100,
      title: "Jam Selesai",
      dataIndex: "end_time",
      render(_, record) {
        return dayjs(record.end_time, "DD/MM/YYYY h:mm A Z").format("HH:MM");
      },
    },
    {
      title: "Status",
      minWidth: 100,
      render(_, record) {
        const startTime = dayjs(record.start_time, "DD/MM/YYYY h:mm A Z");
        const endTime = dayjs(record.end_time, "DD/MM/YYYY h:mm A Z");
        if (startTime.isAfter(dayjs())) return <Tag>Belum dimulai</Tag>;
        if (startTime.isBefore(dayjs()) && endTime.isAfter(dayjs()))
          return <Tag color="warning">Sedang berlangsung</Tag>;
        if (endTime.isBefore(dayjs()))
          return <Tag color="success">Selesai</Tag>;
      },
    },
    {
      title: "Aksi",
      minWidth: 100,
      render(_, record) {
        return (
          <div className="flex gap-2">
            <a
              href={createRedirectUrl({
                payload: {
                  to:
                    currentUser.type === "student" ? "examSession" : "viewExam",
                  exam_id: record.id,
                },
                isDev: env.DEV,
              })}
              target="_blank"
              rel="noreferrer"
            >
              <Button variant="outlined" icon={<Edit03 width={20} />}></Button>
            </a>
          </div>
        );
      },
    },
  ];

  const createExamLink = teaching_plan_id
    ? createRedirectUrl({
        payload: {
          to: "createExamWithExistingTeachingPlanIds",
          teaching_plan_ids: [teaching_plan_id],
        },
        isDev: env.DEV,
      })
    : undefined;

  return (
    <div className="flex flex-col gap-4 pb-16">
      <div className="flex justify-between gap-4">
        <h2>Tambah Rencana Pembelajaran</h2>
        {/* AI Generate Button */}
          <Button
            onClick={handleGenerateFullPlan}
            loading={isGeneratingPlan}
            icon={<Stars02 width={20} />}
          >
            Buat dengan AI
          </Button>
      </div>

      <Form
        onFinish={handleSubmit((values) => {
          if (teaching_plan_id) {
            return updateTeachingPlan({
              ...values,
              teaching_plan_id,
            });
          }
          return createTeachingPlan(values);
        })}
        layout="vertical"
        className="flex flex-col gap-4"
      >
        <div className="text-lg font-semibold">Informasi Pembelajaran</div>
        <FormItem control={control} name="title" label="Judul">
          <Input placeholder="Masukan judul..." />
        </FormItem>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between items-center">
            <div>Deskripsi</div>
            <Button
              size="small"
              onClick={() => handleAIAction("markdown")}
              loading={
                (isGeneratingSection && selectedSection === "markdown") ||
                (isEditingSection && selectedSection === "markdown")
              }
              icon={<Stars02 width={16} />}
              type="text"
            >
              {isSectionEmpty("markdown") ? "Buat dengan AI" : "Edit dengan AI"}
            </Button>
          </div>
          <CrepeEditor
            placeholder="Masukan deskripsi..."
            onMarkdownChange={(markdown) => {
              setValue("markdown", markdown);
            }}
            key={editorRefreshKey}
            defaultValue={watch("markdown")}
          />
        </div>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between gap-2 items-center">
            <div>Bahan Ajar</div>
            <div className="flex gap-2">
              <TeachingMaterialsSelectorButton
                class_id={class_id}
                excludedTeachingMaterialIds={form_teaching_materials.map(
                  (item) => item.id
                )}
                onOk={(teachingMaterialIds) => {
                  setValue("teaching_materials", [
                    ...form_teaching_materials,
                    ...teachingMaterialIds.map((id) => ({ id })),
                  ]);
                }}
              />
              <CreateTeachingMaterialButton
                class_id={class_id}
                onCreateSuccess={(teaching_material_id) => {
                  setValue("teaching_materials", [
                    ...form_teaching_materials,
                    {
                      id: teaching_material_id,
                    },
                  ]);
                }}
                renderTrigger={(onClick) => {
                  return (
                    <Button
                      onClick={onClick}
                      icon={<Plus />}
                      variant="solid"
                      color="primary"
                    >
                      Buat baru
                    </Button>
                  );
                }}
              />
            </div>
          </div>
          <Table
            rowKey={(record) => record.id}
            bordered
            columns={columnsForTeachingMaterials}
            dataSource={filteredTeachingMaterials}
            pagination={false}
          />
        </div>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between gap-2 items-center">
            <div>Tugas</div>
            <div className="flex gap-2">
              <CreateTaskButton
                class_id={class_id}
                onCreateSuccess={(task_id) => {
                  setValue("tasks", [
                    ...form_tasks,
                    {
                      id: task_id,
                    },
                  ]);
                }}
                renderTrigger={(onClick) => {
                  return (
                    <Button
                      onClick={onClick}
                      icon={<Plus />}
                      variant="solid"
                      color="primary"
                    >
                      Buat baru
                    </Button>
                  );
                }}
              />
            </div>
          </div>
          <Table
            rowKey={(record) => record.id}
            bordered
            columns={columnsForTasks}
            dataSource={filteredTasks}
            pagination={false}
          />
        </div>
        {teaching_plan_id && (
          <div className="flex flex-col gap-2 pb-8">
            <div className="flex justify-between gap-2 items-center">
              <div>Ujian</div>
              <div className="flex gap-2">
                <a href={createExamLink} target="_blank" rel="noreferrer">
                  <Button icon={<Plus />} variant="solid" color="primary">
                    Buat baru
                  </Button>
                </a>
              </div>
            </div>
            <Table
              rowKey={(record) => record.id}
              bordered
              columns={columnsForExams}
              dataSource={exams}
              pagination={false}
            />
          </div>
        )}
        <div className="text-lg font-semibold">Informasi Administratif</div>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between items-center">
            <div>Tujuan Pembelajaran</div>
            <Button
              size="small"
              onClick={() => handleAIAction("teaching_goal")}
              loading={
                (isGeneratingSection && selectedSection === "teaching_goal") ||
                (isEditingSection && selectedSection === "teaching_goal")
              }
              icon={<Stars02 width={16} />}
              type="text"
            >
              {isSectionEmpty("teaching_goal") ? "Buat dengan AI" : "Edit dengan AI"}
            </Button>
          </div>
          <CrepeEditor
            placeholder="Masukan tujuan pembelajaran..."
            onMarkdownChange={(markdown) => {
              setValue("teaching_goal", markdown);
            }}
            key={editorRefreshKey}
            defaultValue={watch("teaching_goal")}
          />
        </div>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between items-center">
            <div>Kegiatan Pembelajaran</div>
            <Button
              size="small"
              onClick={() => handleAIAction("teaching_activity")}
              loading={
                (isGeneratingSection && selectedSection === "teaching_activity") ||
                (isEditingSection && selectedSection === "teaching_activity")
              }
              icon={<Stars02 width={16} />}
              type="text"
            >
              {isSectionEmpty("teaching_activity") ? "Buat dengan AI" : "Edit dengan AI"}
            </Button>
          </div>
          <CrepeEditor
            placeholder="Masukan kegiatan pembelajaran..."
            onMarkdownChange={(markdown) => {
              setValue("teaching_activity", markdown);
            }}
            key={editorRefreshKey}
            defaultValue={watch("teaching_activity")}
          />
        </div>
        <div className="flex flex-col gap-2 pb-8">
          <div className="flex justify-between items-center">
            <div>Penilaian</div>
            <Button
              size="small"
              onClick={() => handleAIAction("teaching_scoring")}
              loading={
                (isGeneratingSection && selectedSection === "teaching_scoring") ||
                (isEditingSection && selectedSection === "teaching_scoring")
              }
              icon={<Stars02 width={16} />}
              type="text"
            >
              {isSectionEmpty("teaching_scoring") ? "Buat dengan AI" : "Edit dengan AI"}
            </Button>
          </div>
          <CrepeEditor
            placeholder="Masukan penilaian pembelajaran..."
            onMarkdownChange={(markdown) => {
              setValue("teaching_scoring", markdown);
            }}
            key={editorRefreshKey}
            defaultValue={watch("teaching_scoring")}
          />
        </div>
        <Link
          to="/classes/$class_id"
          params={{
            class_id: class_id,
          }}
          className="contents"
        >
          <Button>Kembali</Button>
        </Link>
        <Button htmlType="submit" variant="solid" color="primary">
          Simpan
        </Button>
      </Form>

      {/* AI Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <Stars02 width={20} />
            {aiModalType === "generate"
              ? "Buat Rencana Pembelajaran dengan AI"
              : aiModalType === "generateSection"
              ? "Buat Bagian dengan AI"
              : "Edit dengan AI"}
          </div>
        }
        open={isAIModalVisible}
        onOk={handleAIModalOk}
        onCancel={handleAIModalCancel}
        okText={aiModalType === "generate" ? "Buat" : aiModalType === "generateSection" ? "Buat" : "Edit"}
        cancelText="Batal"
        confirmLoading={isGeneratingPlan || isEditingSection || isGeneratingSection}
        width={600}
      >
        {aiModalType === "generate" ? (
          <div className="flex flex-col gap-4">
            <div>
              <div className="mb-2 font-medium">Materi Utama:</div>
              <Input
                value={aiTitle}
                onChange={(e) => setAiTitle(e.target.value)}
                placeholder="Masukan judul rencana pembelajaran..."
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="mb-2 font-medium">Nama Kelas:</div>
                <Input
                  value={className}
                  readOnly
                  placeholder="Nama kelas"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Tingkatan:</div>
                <Input
                  value={grade}
                  readOnly
                  placeholder="Tingkatan"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Kurikulum:</div>
                <Input
                  value={curriculumName}
                  readOnly
                  placeholder="Kurikulum"
                />
              </div>
            </div>
            <div>
              <div className="mb-2 font-medium">Mata Pelajaran:</div>
              <Input
                value={subjectName}
                readOnly
                placeholder="Mata pelajaran"
              />
            </div>
            <div>
              <div className="mb-2 font-medium">
                Spesifikasi tambahan (opsional):
              </div>
              <Input.TextArea
                value={aiSpecifications}
                onChange={(e) => setAiSpecifications(e.target.value)}
                placeholder="Masukan spesifikasi khusus untuk rencana pembelajaran, misalnya: 'Fokus pada pembelajaran interaktif', 'Sesuaikan untuk siswa SMA kelas 10', dll."
                rows={4}
              />
            </div>
            <div className="text-sm text-gray-600">
              AI akan membuat rencana pembelajaran lengkap berdasarkan materi
              dan spesifikasi yang Anda berikan.
            </div>
          </div>
        ) : aiModalType === "generateSection" ? (
          <div className="flex flex-col gap-4">
            <div>
              <div className="mb-2 font-medium">Judul Rencana Pembelajaran:</div>
              <Input
                value={aiTitle}
                onChange={(e) => setAiTitle(e.target.value)}
                placeholder="Masukan judul rencana pembelajaran..."
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="mb-2 font-medium">Nama Kelas:</div>
                <Input
                  value={className}
                  readOnly
                  placeholder="Nama kelas"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Tingkatan:</div>
                <Input
                  value={grade}
                  readOnly
                  placeholder="Tingkatan"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Kurikulum:</div>
                <Input
                  value={curriculumName}
                  readOnly
                  placeholder="Kurikulum"
                />
              </div>
            </div>
            <div>
              <div className="mb-2 font-medium">Mata Pelajaran:</div>
              <Input
                value={subjectName}
                readOnly
                placeholder="Mata pelajaran"
              />
            </div>
            <div>
              <div className="mb-2 font-medium">
                Spesifikasi tambahan (opsional):
              </div>
              <Input.TextArea
                value={aiSpecifications}
                onChange={(e) => setAiSpecifications(e.target.value)}
                placeholder="Masukan spesifikasi khusus untuk bagian ini, misalnya: 'Fokus pada metode interaktif', 'Sesuaikan untuk siswa SMA', dll."
                rows={4}
              />
            </div>
            <div className="text-sm text-gray-600">
              AI akan membuat bagian {selectedSection === "markdown" ? "Deskripsi" :
                selectedSection === "teaching_goal" ? "Tujuan Pembelajaran" :
                selectedSection === "teaching_activity" ? "Kegiatan Pembelajaran" :
                selectedSection === "teaching_scoring" ? "Penilaian" : "yang dipilih"} berdasarkan judul dan spesifikasi yang Anda berikan.
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="mb-2 font-medium">Nama Kelas:</div>
                <Input
                  value={className}
                  readOnly
                  placeholder="Nama kelas"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Tingkatan:</div>
                <Input
                  value={grade}
                  readOnly
                  placeholder="Tingkatan"
                />
              </div>
              <div>
                <div className="mb-2 font-medium">Kurikulum:</div>
                <Input
                  value={curriculumName}
                  readOnly
                  placeholder="Kurikulum"
                />
              </div>
            </div>
            <div>
              <div className="mb-2 font-medium">Mata Pelajaran:</div>
              <Input
                value={subjectName}
                readOnly
                placeholder="Mata pelajaran"
              />
            </div>
            <div>
              <div className="mb-2 font-medium">Instruksi edit:</div>
              <Input.TextArea
                value={aiInstructions}
                onChange={(e) => setAiInstructions(e.target.value)}
                placeholder="Masukan instruksi untuk mengedit bagian ini, misalnya: 'Buat lebih detail', 'Sederhanakan bahasa', 'Tambahkan contoh praktis', dll."
                rows={4}
              />
            </div>
            <div className="text-sm text-gray-600">
              AI akan mengedit bagian yang dipilih sesuai dengan instruksi Anda.
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
