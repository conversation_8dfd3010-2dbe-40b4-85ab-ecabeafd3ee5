import { ErrorMessage } from "@hookform/error-message";
import { valibotResolver } from "@hookform/resolvers/valibot";
import type {
  AIEditedExamQuestion,
  EditExamQuestionPayload,
  GenerateExamQuestionsPayload,
  GenerateSingleExamQuestionPayload,
} from "@sisva/api/ai";
import type { ExamQuestionPayload } from "@sisva/api/exam";
import {
  useEditExamQuestion,
  useGenerateExamQuestions,
  useGenerateSingleExamQuestion,
} from "@sisva/hooks/query/exam/useAi"; // Import your AI hooks
import {
  useExamQuestions,
  useSetExamQuestions,
} from "@sisva/hooks/query/exam/useExamQuestions";
import { useExam, useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useNotificationAPI } from "@sisva/providers";
import type {
  ExamOptionQuestion,
  ExamScaleQuestion,
  ExamTextQuestion,
} from "@sisva/types/apiTypes";
import { getRouteApi } from "@tanstack/react-router";
import { ChevronLeft, ChevronRight, Edit02, Grid01, ImageUp, ImageX, Stars02, Trash01 } from "@untitled-ui/icons-react";
import { useToggle } from "ahooks";
import {
  Alert,
  Button,
  Checkbox,
  Divider,
  Empty,
  Form,
  Image,
  Input,
  InputNumber,
  Modal,
  Select,
  Skeleton,
  Space,
  theme,
  Tooltip,
  Upload,
} from "antd";
import { useEffect, useState, useRef } from "react";
import {
  type Control,
  type FieldErrors,
  useFieldArray,
  type UseFieldArrayRemove,
  type UseFieldArrayUpdate,
  useForm,
  type UseFormClearErrors,
  type UseFormSetValue,
  useWatch,
} from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  array,
  boolean,
  forward,
  type InferInput,
  type InferOutput,
  literal,
  minValue,
  nonEmpty,
  number,
  object,
  optional,
  partialCheck,
  pipe,
  string,
  union,
} from "valibot";

const option: ExamOptionQuestion["type"] = "option";
const text: ExamTextQuestion["type"] = "text";
const scale: ExamScaleQuestion["type"] = "scale";

type ExamQuestionType = typeof option | typeof text | typeof scale;

const schema = object({
  questions: array(
    pipe(
      object({
        id: optional(string()),
        text: pipe(string(), nonEmpty("Soal harus diisi")),
        type: union([literal(option), literal(text), literal(scale)]),
        image_url: optional(string()),
        weight: pipe(number(), minValue(1, "Bobot soal harus lebih dari 0")),
        option_detail: optional(
          object({
            options: array(
              object({
                temp_id: string(),
                id: optional(string()),
                text: pipe(string(), nonEmpty("Opsi harus diisi")),
                correct: optional(boolean()),
              })
            ),
          })
        ),
        scale_detail: optional(
          object({
            min_label: pipe(string(), nonEmpty("Min label harus diisi")),
            max_label: pipe(string(), nonEmpty("Max label harus diisi")),
          })
        ),
      }),
      forward(
        partialCheck(
          [["type"], ["option_detail"]],
          ({ type, option_detail }) => {
            if (
              type === "option" &&
              (!option_detail || option_detail.options.length < 2)
            )
              return false;
            return true;
          },
          "Opsi harus lebih dari 1"
        ),
        ["option_detail"]
      ),
      forward(
        partialCheck(
          [["type"], ["option_detail"]],
          ({ type, option_detail }) => {
            const correctExist = option_detail?.options.some(
              (option) => option.correct
            );
            if (
              type === "option" &&
              !!option_detail?.options.length &&
              !correctExist
            )
              return false;
            return true;
          },
          "Pilih setidaknya satu kunci jawaban"
        ),
        ["option_detail"]
      ),
      forward(
        partialCheck(
          [["type"], ["scale_detail"]],
          ({ type, scale_detail }) => {
            if (type === "scale" && !scale_detail) return false;
            return true;
          },
          "Min label dan max label harus diisi"
        ),
        ["scale_detail"]
      )
    )
  ),
});

type SchemaInput = InferInput<typeof schema>;
type SchemaOutput = InferOutput<typeof schema>;

// AI Generation Modal Component
function AIGenerationModal({
  open,
  onCancel,
  onGenerate,
  loading,
  examName,
  subjectName,
  className,
  description,
}: {
  open: boolean;
  onCancel: () => void;
  onGenerate: (payload: GenerateExamQuestionsPayload) => void;
  loading: boolean;
  examName?: string;
  subjectName?: string;
  className?: string;
  description?: string;
}) {
  //TODO: please change to react-hook-form
  const [form] = Form.useForm();

  const handleGenerate = () => {
    form.validateFields().then((values) => {
      onGenerate(values);
    });
  };

  return (
    <Modal
      title={
        <Space>
          <Stars02 width={20} />
          Generate Soal dengan AI
        </Space>
      }
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Batal
        </Button>,
        <Button
          key="generate"
          type="primary"
          loading={loading}
          onClick={handleGenerate}
          icon={<Stars02 width={16} />}
        >
          Generate Soal
        </Button>,
      ]}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          subjectName: subjectName || "",
          questionCount: 5,
          name: examName || "",
          className: className || "",
          description: description || "",
          optionQuestionCount: 3,
          textQuestionCount: 1,
          scaleQuestionCount: 1,
        }}
      >
        <Form.Item
          name="subjectName"
          label="Mata Pelajaran"
          rules={[{ required: true, message: "Mata pelajaran harus diisi" }]}
        >
          <Input placeholder="Contoh: Matematika, Bahasa Indonesia, IPA" />
        </Form.Item>

        <Form.Item
          name="questionCount"
          label="Jumlah Soal"
          rules={[{ required: true, message: "Jumlah soal harus diisi" }]}
        >
          <InputNumber min={1} max={50} style={{ width: "100%" }} />
        </Form.Item>

        <div className="grid grid-cols-3 gap-4">
          <Form.Item name="optionQuestionCount" label="Pilihan Ganda">
            <InputNumber min={0} style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item name="textQuestionCount" label="Isian">
            <InputNumber min={0} style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item name="scaleQuestionCount" label="Skala">
            <InputNumber min={0} style={{ width: "100%" }} />
          </Form.Item>
        </div>

        <Form.Item name="name" label="Nama Ujian (Opsional)">
          <Input placeholder="Nama ujian untuk konteks AI" />
        </Form.Item>

        <Form.Item name="className" label="Kelas (Opsional)">
          <Input placeholder="Contoh: Kelas 10, SMA, SD" />
        </Form.Item>

        <Form.Item name="description" label="Deskripsi Tambahan (Opsional)">
          <Input.TextArea
            rows={3}
            placeholder="Deskripsi materi atau fokus soal yang diinginkan"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

// Single Question AI Generation Modal
// Updated Single Question AI Generation Modal with dropdown
function SingleQuestionAIModal({
  open,
  onCancel,
  onGenerate,
  loading,
  examName,
  subjectName,
  className,
  description,
}: {
  open: boolean;
  onCancel: () => void;
  onGenerate: (payload: GenerateSingleExamQuestionPayload) => void;
  loading: boolean;
  examName?: string;
  subjectName?: string;
  className?: string;
  description?: string;
}) {
  //TODO: please change to react-hook-form
  const [form] = Form.useForm();

  const handleGenerate = () => {
    form.validateFields().then((values) => {
      onGenerate({
        ...values,
        questionType: values.questionType,
      });
    });
  };

  const questionTypeOptions = [
    { label: "Pilihan Ganda", value: "option" },
    { label: "Isian", value: "text" },
    { label: "Skala", value: "scale" },
  ];

  return (
    <Modal
      title={
        <Space>
          <Stars02 width={20} />
          Buat Soal dengan AI
        </Space>
      }
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Batal
        </Button>,
        <Button
          key="generate"
          type="primary"
          loading={loading}
          onClick={handleGenerate}
          icon={<Stars02 width={16} />}
        >
          Buat Soal
        </Button>,
      ]}
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          subjectName: subjectName || "",
          name: examName || "",
          className: className || "",
          description: description || "",
          customization: "",
          questionType: "text", // Default to text
        }}
      >
        <Form.Item
          name="questionType"
          label="Jenis Soal"
          rules={[{ required: true, message: "Pilih jenis soal" }]}
        >
          <Select
            options={questionTypeOptions}
            placeholder="Pilih jenis soal yang akan dibuat"
          />
        </Form.Item>

        <Form.Item
          name="subjectName"
          label="Mata Pelajaran"
          rules={[{ required: true, message: "Mata pelajaran harus diisi" }]}
        >
          <Input placeholder="Contoh: Matematika, Bahasa Indonesia, IPA" />
        </Form.Item>

        <Form.Item name="name" label="Nama Ujian (Opsional)">
          <Input placeholder="Nama ujian untuk konteks AI" />
        </Form.Item>

        <Form.Item name="className" label="Kelas (Opsional)">
          <Input placeholder="Contoh: Kelas 10, SMA, SD" />
        </Form.Item>

        <Form.Item name="description" label="Deskripsi Materi (Opsional)">
          <Input.TextArea
            rows={2}
            placeholder="Deskripsi materi atau topik yang ingin difokuskan"
          />
        </Form.Item>

        <Form.Item name="customization" label="Kustomisasi Soal (Opsional)">
          <Input.TextArea
            rows={3}
            placeholder="Instruksi khusus untuk AI dalam membuat soal ini"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

// AI Edit Question Modal Component
function AIEditQuestionModal({
  open,
  onCancel,
  onEdit,
  loading,
  currentQuestion,
  examName,
  subjectName,
  className,
  description,
}: {
  open: boolean;
  onCancel: () => void;
  onEdit: (payload: EditExamQuestionPayload) => void;
  loading: boolean;
  currentQuestion: SchemaOutput["questions"][number] | null;
  examName?: string;
  subjectName?: string;
  className?: string;
  description?: string;
}) {
  const [form] = Form.useForm();

  useEffect(() => {
    if (currentQuestion) {
      form.setFieldsValue({
        instructions: "",
        subjectName: subjectName || "",
        className: className || "",
        examName: examName || "",
        examDescription: description || "",
      });
    }
  }, [currentQuestion, form, examName, subjectName, className, description]);

  const handleEdit = () => {
    if (!currentQuestion) return;
    form.validateFields().then((values) => {
      const payload: EditExamQuestionPayload = {
        currentQuestion: {
          text: currentQuestion.text,
          type: currentQuestion.type,
          weight: currentQuestion.weight,
          option_detail: currentQuestion.option_detail
            ? {
                options: currentQuestion.option_detail.options.map((opt) => ({
                  text: opt.text,
                  correct: opt.correct ?? false,
                })),
              }
            : undefined,
          scale_detail: currentQuestion.scale_detail
            ? {
                min_label: currentQuestion.scale_detail.min_label,
                max_label: currentQuestion.scale_detail.max_label,
              }
            : undefined,
        },
        instructions: values.instructions,
        subjectName: values.subjectName,
        className: values.className,
        examName: values.examName,
        examDescription: values.examDescription,
      };
      onEdit(payload);
    });
  };

  if (!currentQuestion) return null;

  return (
    <Modal
      title={
        <Space>
          <Stars02 width={20} />
          Edit Soal dengan AI
        </Space>
      }
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Batal
        </Button>,
        <Button
          key="edit"
          type="primary"
          loading={loading}
          onClick={handleEdit}
          icon={<Stars02 width={16} />}
        >
          Edit Soal
        </Button>,
      ]}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item label="Soal Saat Ini">
          <Input.TextArea value={currentQuestion.text} readOnly rows={3} />
        </Form.Item>
        <Form.Item
          name="instructions"
          label="Instruksi Perubahan"
          rules={[{ required: true, message: "Instruksi harus diisi" }]}
        >
          <Input.TextArea
            rows={3}
            placeholder="Contoh: Buat soal ini menjadi lebih sulit, ubah menjadi soal cerita, perbaiki tata bahasa"
          />
        </Form.Item>
        <Form.Item name="subjectName" label="Mata Pelajaran (Opsional)">
          <Input placeholder="Konteks mata pelajaran untuk AI" />
        </Form.Item>
        <Form.Item name="className" label="Kelas (Opsional)">
          <Input placeholder="Konteks kelas untuk AI" />
        </Form.Item>
        <Form.Item name="examName" label="Nama Ujian (Opsional)">
          <Input placeholder="Konteks nama ujian untuk AI" />
        </Form.Item>
        <Form.Item name="examDescription" label="Deskripsi Ujian (Opsional)">
          <Input.TextArea rows={2} placeholder="Konteks deskripsi ujian untuk AI" />
        </Form.Item>
      </Form>
    </Modal>
  );
}

// Question Navigation Component
function QuestionNavigation({
  questions,
  selectedIndex,
  onQuestionSelect,
  edit,
  questionRefs,
}: {
  questions: SchemaOutput["questions"];
  selectedIndex: number;
  onQuestionSelect: (index: number) => void;
  edit: boolean;
  questionRefs: React.MutableRefObject<Record<number, HTMLDivElement | null>>; // Add this type
}) {
  const token = theme.useToken().token;

  const scrollToQuestion = (index: number) => {
    questionRefs.current[index]?.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  };

  return (
    <div className="w-64 sticky top-4 self-start">
      <div className="flex flex-col gap-4 border border-solid border-neutral-200 shadow-md rounded-md p-4 bg-white">
        <div className="font-medium text-center">Navigasi Soal</div>
        
        {/* Scrollable container dengan max-height yang lebih besar */}
        <div className="overflow-auto max-h-[70vh] pe-2">
          <div className="grid grid-cols-4 gap-2">
            {questions.map((_, index) => (
              <div
                key={index}
                onClick={() => {
                  onQuestionSelect(index);
                  scrollToQuestion(index);
                }}
                className={`
                  border cursor-pointer text-lg border-solid rounded-md flex flex-col items-center overflow-hidden
                  hover:shadow-md transition-all duration-200
                  ${selectedIndex === index ? 'border-blue-500 bg-blue-50' : 'border-neutral-300 hover:border-blue-300'}
                `}
              >
                <div className="p-2">{index + 1}</div>
                <div
                  className="w-full h-2"
                  style={{
                    backgroundColor: selectedIndex === index ? token.colorPrimary : token.colorTextDescription,
                  }}
                />
              </div>
            ))}
          </div>
        </div>
        
        {edit && (
          <div className="text-xs text-neutral-500 text-center border-t pt-2">
            Klik nomor soal untuk navigasi
          </div>
        )}
      </div>
    </div>
  );
}

export default function SoalTab() {
  const notification = useNotificationAPI();
  const routeApi = getRouteApi("/(main)/_layout/exams/$exam_id/_adminAuth/");
  const { exam_id } = routeApi.useParams();
  const { data: exam, isLoading: L2 } = useExam({ exam_id });
  const { data: examWithClasses } = useExamWithClasses({ exam_id });
  const { data: examQuestions = [], isLoading: L1 } = useExamQuestions({
    exam_id,
  });
  const isLoading = L1 || L2;

  const notEnoughQuestion = exam
    ? examQuestions.length < exam.display_question_num
    : false;

  // Extract data for AI generation auto-fill
  const subjectName = examWithClasses?.classes?.[0]?.subject_name || "";
  const className = examWithClasses?.classes?.map(c => c.student_group_name).join(", ") || "";
  const examDescription = examWithClasses?.description || "";

  const [edit, { toggle, setRight }] = useToggle();
  const [aiModalOpen, setAiModalOpen] = useState(false);
  const [singleAiModalOpen, setSingleAiModalOpen] = useState(false);
    useState<ExamQuestionType>("text");
  const [aiEditModalOpen, setAiEditModalOpen] = useState(false);
  const [editingQuestionIndex, setEditingQuestionIndex] = useState<number | null>(
    null
  );
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const questionRefs = useRef<Record<number, HTMLDivElement | null>>({});

  // activate edit mode when there is no question
  useEffect(() => {
    if (!isLoading && examQuestions.length === 0) {
      setRight();
    }
  }, [examQuestions.length, isLoading, setRight]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    clearErrors,
    reset,
    setValue,
    getValues,
  } = useForm<SchemaInput, unknown, SchemaOutput>({
    values: {
      questions: examQuestions.map((question) => {
        if (question.type === "option") {
          return {
            ...question,
            option_detail: {
              options: question.option_detail.options.map((option) => ({
                ...option,
                temp_id: option.id,
              })),
            },
          };
        }

        return question;
      }),
    },
    resolver: valibotResolver(schema),
  });

  const { fields, append, update, remove } = useFieldArray({
    control,
    name: "questions",
  });

  const { mutate: setExamQuestions } = useSetExamQuestions();

  // AI Generation hooks
  const { mutate: generateExamQuestions, isPending: isGeneratingMultiple } =
    useGenerateExamQuestions();

  const { mutate: generateSingleQuestion, isPending: isGeneratingSingle } =
    useGenerateSingleExamQuestion();

  const { mutate: editExamQuestionAI, isPending: isEditingWithAI } =
    useEditExamQuestion();

  const onFinish = handleSubmit((value) => {
    const payload = value.questions.map((question) => {
      if (question.type === "option") {
        return {
          ...question,
          scale_detail: undefined,
        };
      }

      if (question.type === "scale") {
        return {
          ...question,
          option_detail: undefined,
        };
      }

      if (question.type === "text") {
        return {
          ...question,
          scale_detail: undefined,
          option_detail: undefined,
        };
      }
    }) as ExamQuestionPayload[];

    setExamQuestions(
      {
        data: payload,
        exam_id,
      },
      {
        onSuccess() {
          notification.success({ message: "Soal berhasil diperbarui" });
          toggle();
        },
        onError() {
          notification.error({ message: "Soal gagal diperbarui" });
        },
      }
    );
  });

  const handleSingleAIGenerate = (questionType: ExamQuestionType) => {
    setSingleAiModalOpen(true);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Soal</h2>
        {edit && (
          <Space>
            <Tooltip title="Generate multiple questions with AI">
              <Button
                icon={<Stars02 width={16} />}
                onClick={() => setAiModalOpen(true)}
                loading={isGeneratingMultiple}
              >
                Generate Semua Soal dengan AI
              </Button>
            </Tooltip>
          </Space>
        )}
      </div>
      {notEnoughQuestion && (
        <Alert
          showIcon
          type="warning"
          message={
            <span>
              Jumlah soal yang ada masih kurang dari{" "}
              <span className="font-semibold">
                {exam?.display_question_num}
              </span>
            </span>
          }
        />
      )}

      <Form layout="vertical" onFinish={onFinish}>
        <div className="size-full flex gap-4">
          {/* Question Navigation - Only show when there are questions */}
          {fields.length > 0 && (
            <QuestionNavigation
              questions={fields}
              selectedIndex={selectedQuestionIndex}
              onQuestionSelect={setSelectedQuestionIndex}
              edit={edit}
              questionRefs={questionRefs}
            />
          )}
          
          <div className="flex-1 flex flex-col gap-4">
            <div>
              {(() => {
                if (isLoading) {
                  return <Skeleton active />;
                }
                if (fields.length === 0 && examQuestions.length === 0) {
                  return (
                    <Empty description="Soal belum tersedia, silahkan tambah soal terlebih dahulu" />
                  );
                }

                return fields.map((field, index) => {
                  return (
                    <div
                      key={field.id}
                      ref={(el) => {
                        questionRefs.current[index] = el;
                      }}
                    >
                      <QuestionForm
                        index={index}
                        setValue={setValue}
                        errors={errors}
                        edit={edit}
                        control={control}
                        clearErrors={clearErrors}
                        remove={remove}
                        update={update}
                        onEditWithAI={(index) => {
                          setEditingQuestionIndex(index);
                          setAiEditModalOpen(true);
                        }}
                        setSingleAiModalOpen={setSingleAiModalOpen}
                      />
                    </div>
                  );
                });
              })()}
            </div>
          <div className="flex justify-end gap-4 border-t pt-4 pb-4 bg-white sticky bottom-0">
            {!edit && (
              <Button color="primary" variant="solid" onClick={toggle}>
                Edit
              </Button>
            )}
            {edit && (
              <>
                <div className="flex gap-2">
                  <Button
                    color="primary"
                    variant="outlined"
                    onClick={() => {
                      const questions = getValues("questions");
                      const lastQuestion = questions?.[questions.length - 1];

                      append({
                        text: "",
                        type: lastQuestion?.type ?? "text",
                        weight: 0,
                      });
                    }}
                  >
                    Tambah soal
                  </Button>
                    {/* Single unified AI button */}
                    <Tooltip title="Buat soal dengan AI">
                      <Button
                        icon={<Stars02 width={16} />}
                        onClick={() => setSingleAiModalOpen(true)}
                        loading={isGeneratingSingle}
                      >
                        Buat Soal dengan AI
                      </Button>
                    </Tooltip>
                </div>

                <Button
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    toggle();
                    reset();
                  }}
                >
                  Batal
                </Button>

                <Button color="primary" variant="solid" htmlType="submit">
                  Simpan
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Form>

      {/* AI Generation Modals */}
      <AIGenerationModal
        open={aiModalOpen}
        onCancel={() => setAiModalOpen(false)}
        onGenerate={(payload) =>
          generateExamQuestions(payload, {
            onSuccess: (responseData) => {
              // Changed data to responseData for clarity
              if (
                responseData &&
                responseData.questions &&
                Array.isArray(responseData.questions)
              ) {
                responseData.questions.forEach((generatedQuestion) => {
                  const newQuestion = {
                    text: generatedQuestion.text || "",
                    type: generatedQuestion.type || "text",
                    weight: generatedQuestion.weight || 1,
                    option_detail:
                      generatedQuestion.type === "option"
                        ? {
                            options: (
                              generatedQuestion.option_detail?.options || []
                            ).map((opt) => ({
                              temp_id: crypto.randomUUID(),
                              text: opt.text || "",
                              correct: opt.correct || false,
                            })),
                          }
                        : undefined,
                    scale_detail:
                      generatedQuestion.type === "scale"
                        ? {
                            min_label:
                              generatedQuestion.scale_detail?.min_label || "",
                            max_label:
                              generatedQuestion.scale_detail?.max_label || "",
                          }
                        : undefined,
                  };
                  append(newQuestion);
                      // Check if the last question is empty and delete it if it is
    const questions = getValues("questions"); // Access form values
    if (questions && questions.length > 0 && !questions[questions.length - 1].text) {
      remove(questions.length - 1); // Remove the last question
    }
                });
                notification.success({
                  message: `${responseData.questions.length} soal berhasil di-generate dengan AI`,
                });
              } else {
                notification.error({
                  message:
                    "Format respons AI tidak sesuai untuk generate multiple soal.",
                });
                console.error(
                  "AI Generation Error: Unexpected response format for multiple questions",
                  responseData
                );
              }
              setAiModalOpen(false);
            },
            onError: (error) => {
              notification.error({ message: "Gagal generate soal dengan AI" });
              console.error("AI Generation Error:", error);
            },
          })
        }
        loading={isGeneratingMultiple}
        examName={exam?.name}
        subjectName={subjectName}
        className={className}
        description={examDescription}
      />

      {/* Updated Single Question AI Modal */}
      <SingleQuestionAIModal
        open={singleAiModalOpen}
        onCancel={() => setSingleAiModalOpen(false)}
        onGenerate={(payload) =>
          generateSingleQuestion(payload, {
            onSuccess: (data) => {
              if (data) {
                const newQuestion = {
                  text: data.text || "",
                  type: data.type || payload.questionType,
                  weight: data.weight || 1,
                  option_detail:
                    data.type === "option"
                      ? {
                          options: (data.option_detail?.options || []).map(
                            (opt) => ({
                              temp_id: crypto.randomUUID(),
                              text: opt.text || "",
                              correct: opt.correct || false,
                            })
                          ),
                        }
                      : undefined,
                  scale_detail:
                    data.type === "scale"
                      ? {
                          min_label: data.scale_detail?.min_label || "",
                          max_label: data.scale_detail?.max_label || "",
                        }
                      : undefined,
                };
                append(newQuestion);
                notification.success({
                  message: "Soal berhasil di-generate dengan AI",
                });
              }
              setSingleAiModalOpen(false);
            },
            onError: (error) => {
              notification.error({ message: "Gagal generate soal dengan AI" });
              console.error("Single AI Generation Error:", error);
            },
          })
        }
        loading={isGeneratingSingle}
        examName={exam?.name}
        subjectName={subjectName}
        className={className}
        description={examDescription}
      />

      <AIEditQuestionModal
        open={aiEditModalOpen}
        onCancel={() => {
          setAiEditModalOpen(false);
          setEditingQuestionIndex(null);
        }}
        onEdit={(payload) => {
          if (editingQuestionIndex === null) return;
          editExamQuestionAI(payload, {
            onSuccess: (data) => {
              if (data) {
                const updatedQuestion = {
                  ...getValues(`questions.${editingQuestionIndex}`), // Keep existing ID and other fields
                  text: data.text || "",
                  type: data.type || getValues(`questions.${editingQuestionIndex}.type`),
                  weight: data.weight || getValues(`questions.${editingQuestionIndex}.weight`),
                  option_detail:
                    data.type === "option" && data.option_detail
                      ? {
                          options: data.option_detail.options.map(
                            (opt: { text: string; correct: boolean }) => ({
                              temp_id: crypto.randomUUID(), // Ensure new temp_id for new/edited options
                              text: opt.text || "",
                              correct: opt.correct || false,
                            })
                          ),
                        }
                      : undefined,
                  scale_detail:
                    data.type === "scale" && data.scale_detail
                      ? {
                          min_label: data.scale_detail.min_label || "",
                          max_label: data.scale_detail.max_label || "",
                        }
                      : undefined,
                  // Preserve image_url if not changed by AI
                  image_url: getValues(`questions.${editingQuestionIndex}.image_url`),
                };
                update(editingQuestionIndex, updatedQuestion);
                notification.success({
                  message: "Soal berhasil diedit dengan AI",
                });
              }
              setAiEditModalOpen(false);
              setEditingQuestionIndex(null);
            },
            onError: (error) => {
              notification.error({
                message: "Gagal mengedit soal dengan AI",
              });
              console.error("AI Edit Error:", error);
            },
          });
        }}
        loading={isEditingWithAI}
        currentQuestion={
          editingQuestionIndex !== null
            ? getValues(`questions.${editingQuestionIndex}`)
            : null
        }
        examName={exam?.name}
        subjectName={subjectName}
        className={className}
        description={examDescription}
      />
    </div>
  );
}

function QuestionForm({
  control,
  index,
  setValue,
  clearErrors,
  errors,
  edit,
  remove,
  update,
  onEditWithAI,
  setSingleAiModalOpen,
}: {
  control: Control<SchemaInput>;
  index: number;
  setValue: UseFormSetValue<SchemaInput>;
  clearErrors: UseFormClearErrors<SchemaInput>;
  errors: FieldErrors<SchemaInput>;
  edit: boolean;
  remove: UseFieldArrayRemove;
  update: UseFieldArrayUpdate<SchemaInput>;
  onEditWithAI: (index: number) => void;
  setSingleAiModalOpen: (open: boolean) => void;
}) {
  const token = theme.useToken().token;
  const { mutateAsync: uploadFile } = useUploadFile();
  const question = useWatch({ control, name: `questions.${index}` });
  const getUrl = useGetFileUrl();
  const isQuestionEmpty = !question.text || question.text.trim() === "";
  const examTypeOptions: { label: string; value: ExamQuestionType }[] = [
    {
      label: "Pilihan ganda",
      value: "option",
    },
    {
      label: "Isian",
      value: "text",
    },
    {
      label: "Skala",
      value: "scale",
    },
  ];

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="flex justify-between items-center pb-4">
            <div className="font-medium text-lg">{index + 1}.</div>
              <div className="flex items-center gap-2">
                {edit && (
                  <Tooltip title={isQuestionEmpty ? "Buat soal dengan AI" : "Edit dengan AI"}>
                    <Button
                      icon={<Stars02 width={16} />}
                      onClick={() => {
                        if (isQuestionEmpty) {
                          setSingleAiModalOpen(true);
                        } else {
                          onEditWithAI(index);
                        }
                      }}
                      size="small"
                      type="dashed"
                    >
                      {isQuestionEmpty ? "Buat soal dengan AI" : "Edit dengan AI"}
                    </Button>
                  </Tooltip>
                )}
                {question.image_url && (
                  <Button
                    icon={<ImageX width={20} />}
                    onClick={() => {
                      setValue(`questions.${index}.image_url`, "");
                    }}
                    color="danger"
                    variant="outlined"
                    disabled={!edit}
                  >
                    Hapus Gambar
                  </Button>
                )}
                <Upload
                  beforeUpload={async (file) => {
                    const formData = new FormData();
                    formData.append("file", file);
                    const uri = await uploadFile(formData);
                    setValue(`questions.${index}.image_url`, uri);
                    clearErrors(`questions.${index}.image_url`);
                    return false;
                  }}
                  multiple={false}
                  maxCount={1}
                  accept="image/*"
                  showUploadList={false}
                >
                  <Button
                    icon={<ImageUp width={20} />}
                    color="primary"
                    variant="outlined"
                    disabled={!edit}
                  >
                    Unggah Gambar
                  </Button>
                </Upload>
              </div>
          </div>
          {question.image_url && (
            <div className="pb-4">
              <Image
                src={getUrl(question.image_url)}
                alt="Gambar untuk soal ujian"
                width={100}
                height={100}
                className="min-w-20 object-cover"
              />
            </div>
          )}
          <FormItem control={control} name={`questions.${index}.text`}>
            <Input.TextArea readOnly={!edit} />
          </FormItem>
          <ErrorMessage
            errors={errors}
            name={`questions.${index}.option_detail`}
            render={({ message }) => (
              <div
                style={{
                  color: token.colorErrorText,
                }}
              >
                {message}
              </div>
            )}
          />

          {question.type === "scale" && (
            <div className="grid grid-cols-2 gap-4">
              <FormItem
                label="Min label"
                control={control}
                name={`questions.${index}.scale_detail.min_label`}
              >
                <Input readOnly={!edit} />
              </FormItem>
              <FormItem
                label="Max label"
                control={control}
                name={`questions.${index}.scale_detail.max_label`}
              >
                <Input readOnly={!edit} />
              </FormItem>
            </div>
          )}
          {question.type === "option" &&
            question.option_detail?.options.map((option, option_index) => (
              <div key={option.id ?? option_index} className="flex gap-4">
                <FormItem
                  control={control}
                  valuePropName="checked"
                  name={`questions.${index}.option_detail.options.${option_index}.correct`}
                >
                  <Checkbox
                    disabled={!edit}
                    onChange={() => {
                      clearErrors(`questions.${index}.option_detail`);
                    }}
                  />
                </FormItem>
                <FormItem
                  control={control}
                  name={`questions.${index}.option_detail.options.${option_index}.text`}
                  className="flex-1"
                >
                  <Input readOnly={!edit} />
                </FormItem>
                {edit && (
                  <Button
                    variant="outlined"
                    onClick={() => {
                      if (!question.option_detail) return;
                      update(index, {
                        ...question,
                        option_detail: {
                          options: question.option_detail.options.filter(
                            (old_option) =>
                              old_option.temp_id !== option.temp_id
                          ),
                        },
                      });
                    }}
                    icon={<Trash01 width={20} />}
                  ></Button>
                )}
              </div>
            ))}
          {question.type === "option" && edit && (
            <Button
              onClick={() => {
                const newOption = {
                  text: "",
                  correct: false,
                  temp_id: crypto.randomUUID(),
                };
                update(index, {
                  ...question,
                  option_detail: {
                    options: question.option_detail
                      ? [...question.option_detail.options, newOption]
                      : [newOption],
                  },
                });

                clearErrors(`questions.${index}.option_detail`);
              }}
            >
              Tambah Opsi
            </Button>
          )}
        </div>
        <div className="flex flex-col justify-start w-40">
          <FormItem
            label={<span className="font-medium text-lg">Tipe</span>}
            control={control}
            name={`questions.${index}.type`}
          >
            <Select options={examTypeOptions} open={edit ? undefined : false} />
          </FormItem>
          <FormItem
            label={<span className="font-medium text-lg">Bobot Soal</span>}
            control={control}
            name={`questions.${index}.weight`}
          >
            <InputNumber readOnly={!edit} min={0} max={1000} />
          </FormItem>
          {edit && (
            <Button
              variant="outlined"
              color="danger"
              icon={<Trash01 width={20} />}
              onClick={() => {
                remove(index);
              }}
            >
              Hapus Soal
            </Button>
          )}
        </div>
      </div>
      <Divider />
    </>
  );
}
