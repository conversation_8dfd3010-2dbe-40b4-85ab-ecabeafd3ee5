package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/temui-sisva/ai-service/internal/model"
	"google.golang.org/genai"
)

// AIManager handles AI-related operations
type AIManager struct {
	client *genai.Client
	model  string
}

// NewAIManager creates a new AI manager
func NewAIManager(apiKey string) (*AIManager, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("GEMINI_API_KEY is not provided")
	}

	client, err := genai.NewClient(context.Background(), &genai.ClientConfig{
		APIKey:  apiKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return &AIManager{
		client: client,
		model:  "gemini-2.0-flash", // Default model
	}, nil
}

// GenerateTeachingPlan generates a teaching plan using AI
func (m *AIManager) GenerateTeachingPlan(ctx context.Context, req model.TeachingPlanRequest) (*model.TeachingPlan, error) {
	// Add specifications to the prompt if provided
	specificationText := ""
	if req.Specifications != "" {
		specificationText = fmt.Sprintf("\nSpesifikasi tambahan: %s\n\nBuat rencana pembelajaran yang sesuai dengan spesifikasi di atas.", req.Specifications)
	}

	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan rencana pembelajaran lengkap berdasarkan judul berikut: "%s".%s

Hasilkan konten untuk bagian-bagian berikut dalam format Markdown:


A. deskripsi rencana pembelajaran yang:
	1. Menjelaskan secara detail tentang rencana pembelajaran ini
	2. Mencakup tujuan umum pembelajaran
	3. Menjelaskan manfaat yang akan diperoleh siswa
	4. Memberikan gambaran singkat tentang apa yang akan dipelajari
	5. Sesuai dengan konteks pendidikan Indonesia

B. tujuan pembelajaran yang:
	1. Menjelaskan tujuan pembelajaran spesifik yang ingin dicapai
	2. Mencakup kompetensi yang akan dikembangkan
	3. Menjelaskan hasil yang diharapkan setelah pembelajaran
	4. Menggunakan kata kerja operasional yang terukur
	5. Sesuai dengan standar pendidikan Indonesia

C. kegiatan pembelajaran yang:
	1. Menjelaskan langkah-langkah kegiatan pembelajaran secara terstruktur
	2. Mencakup aktivitas pembuka, inti, dan penutup
	3. Menyertakan metode pengajaran yang sesuai
	4. Menjelaskan alokasi waktu untuk setiap kegiatan
	5. Mencakup interaksi yang diharapkan antara guru dan siswa
	6. Sesuai dengan standar pendidikan Indonesia
D. metode penilaian yang:
	1. Menjelaskan metode penilaian yang akan digunakan
	2. Mencakup kriteria keberhasilan yang jelas
	3. Menjelaskan cara mengukur pencapaian tujuan pembelajaran
	4. Menyertakan berbagai jenis penilaian (formatif, sumatif, dll)
	5. Mencakup rubrik atau pedoman penilaian
E. Tugas yang relevan dengan materi pembelajaran ini. Berikan nama tugas yang jelas dan deskripsi tugas yang detail (termasuk tujuan, instruksi, kriteria penilaian, dan petunjuk pengerjaan).

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "description": "Deskripsi lengkap rencana pembelajaran",
  "teachingGoal": "Tujuan pembelajaran lengkap",
  "teachingActivity": "Kegiatan pembelajaran lengkap",
  "teachingScoring": "Metode penilaian lengkap",
  "tasks": [
    {
      "name": "Nama tugas 1",
      "description": "Deskripsi tugas 1 lengkap"
    }
  ]
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.
Jangan sertakan backtick atau tanda lain di awal atau akhir JSON.`, req.Title, specificationText)

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result model.TeachingPlan
	if err := parseJSONResponse(responseText, &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &result, nil
}

// GenerateTask generates a task using AI
func (m *AIManager) GenerateTask(ctx context.Context, req model.TaskRequest) (*model.Task, error) {
	// Detect if the task is a quiz/exam based on the title
	isQuiz := containsAny(req.Title, []string{"kuis", "ujian", "tes", "soal", "pilihan ganda", "multiple choice", "quiz", "test", "exam"})

	// Build context information for the prompt
	contextInfo := buildContextInfo(req)

	// Add specifications to the prompt if provided
	specificationText := ""
	if req.Specifications != "" {
		specificationText = fmt.Sprintf("\nSpesifikasi tambahan: %s", req.Specifications)
	}

	var prompt string
	if isQuiz {
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan kuis/ujian pembelajaran berdasarkan judul berikut: "%s".%s%s

Hasilkan konten untuk bagian-bagian berikut:

1. Nama Tugas: Berikan nama kuis/ujian yang jelas dan sesuai dengan judul yang diberikan.

2. Deskripsi Tugas: Jelaskan secara detail tentang kuis/ujian ini, termasuk:
   - Tujuan kuis/ujian
   - Instruksi yang jelas tentang apa yang harus dilakukan siswa
   - Kriteria penilaian
   - Petunjuk pengerjaan
   - Sumber daya yang dapat digunakan (jika ada)

3. Soal-soal: Buatkan 5 soal pilihan ganda yang relevan dengan judul kuis/ujian dan konteks pembelajaran. Untuk setiap soal, sertakan:
   - Pertanyaan
   - 4 pilihan jawaban (A, B, C, D)
   - JANGAN sertakan jawaban yang benar atau kunci jawaban

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "name": "Nama kuis/ujian lengkap",
  "description": "Deskripsi kuis/ujian lengkap dalam format Markdown, termasuk soal-soal pilihan ganda dengan format yang rapi"
}

Pastikan soal-soal pilihan ganda diformat dengan baik dalam deskripsi menggunakan Markdown, dengan nomor soal, pertanyaan, dan pilihan jawaban (A, B, C, D). JANGAN sertakan kunci jawaban.
Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.
Jangan sertakan backtick atau tanda lain di awal atau akhir JSON.`, req.Title, contextInfo, specificationText)
	} else {
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan tugas pembelajaran berdasarkan judul berikut: "%s".%s%s

Hasilkan konten untuk bagian-bagian berikut:

1. Nama Tugas: Berikan nama tugas yang jelas dan sesuai dengan judul yang diberikan.

2. Deskripsi Tugas: Jelaskan secara detail tentang tugas ini, termasuk:
   - Tujuan tugas
   - Instruksi yang jelas tentang apa yang harus dilakukan siswa
   - Kriteria penilaian
   - Petunjuk pengerjaan
   - Sumber daya yang dapat digunakan (jika ada)

3. Soal yang sesuai dengan instruksi

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "name": "Nama tugas lengkap",
  "description": "Deskripsi tugas lengkap dengan format yang rapih, JANGAN MENGGUNAKAN MARKDOWN"
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.
Jangan sertakan backtick atau tanda lain di awal atau akhir JSON.`, req.Title, contextInfo, specificationText)
	}

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result model.Task
	if err := parseJSONResponse(responseText, &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &result, nil
}

// EditTask edits a task using AI
func (m *AIManager) EditTask(ctx context.Context, req model.EditTaskRequest) (*model.Task, error) {
	// Serialize current task to JSON to include in the prompt
	currentTaskJSON, err := json.MarshalIndent(req.CurrentTask, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to serialize current task: %w", err)
	}

	// Build context information for the prompt
	contextInfo := ""
	if req.PlanTitle != "" || req.SubjectName != "" || req.ClassName != "" {
		var contextParts []string

		if req.PlanTitle != "" {
			contextParts = append(contextParts, fmt.Sprintf("rencana pembelajaran \"%s\"", req.PlanTitle))
		}

		if req.SubjectName != "" {
			contextParts = append(contextParts, fmt.Sprintf("mata pelajaran \"%s\"", req.SubjectName))
		}

		if req.ClassName != "" {
			contextParts = append(contextParts, fmt.Sprintf("kelas \"%s\"", req.ClassName))
		}

		if len(contextParts) > 0 {
			contextInfo = fmt.Sprintf("\nKonteks: Tugas ini adalah bagian dari %s.", joinWithFor(contextParts))
		}
	}

	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, bantu edit tugas pembelajaran berikut.

Tugas saat ini:
%s

Instruksi untuk pengeditan:
%s%s

Hasilkan tugas yang sudah diedit sesuai instruksi di atas.

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "name": "Nama tugas yang sudah diedit",
  "description": "Deskripsi tugas yang sudah diedit dengan format yang rapi, JANGAN MENGGUNAKAN MARKDOWN"
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.
Jangan sertakan backtick atau tanda lain di awal atau akhir JSON.`, string(currentTaskJSON), req.Instructions, contextInfo)

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result model.Task
	if err := parseJSONResponse(responseText, &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &result, nil
}

// GenerateExamQuestions generates exam questions using AI
func (m *AIManager) GenerateExamQuestions(ctx context.Context, req model.ExamQuestionsRequest) (*model.ExamQuestions, error) {
	// Calculate question distribution
	totalSpecifiedCount := req.OptionQuestionCount + req.TextQuestionCount + req.ScaleQuestionCount

	// If specific counts are provided and they add up to the total, use them
	// Otherwise, use default distribution
	optionCount := req.OptionQuestionCount
	textCount := req.TextQuestionCount
	scaleCount := req.ScaleQuestionCount

	if totalSpecifiedCount != req.QuestionCount {
		// Default distribution if specific counts aren't provided or don't match total
		optionCount = int(float64(req.QuestionCount) * 0.6)
		textCount = int(float64(req.QuestionCount) * 0.3)
		scaleCount = req.QuestionCount - optionCount - textCount // Ensure total adds up exactly
	}

	// Build exam info text
	examInfoText := ""
	if req.Name != "" {
		examInfoText += fmt.Sprintf("- Nama Ujian: \"%s\"\n", req.Name)
	}

	if req.ClassName != "" {
		examInfoText += fmt.Sprintf("- Kelas: \"%s\"\n", req.ClassName)
	}

	// Add exam description if provided
	examDescriptionText := ""
	if req.Description != "" {
		examDescriptionText = fmt.Sprintf(`
Deskripsi Ujian:
%s

Pastikan soal yang dihasilkan sesuai dengan deskripsi ujian di atas.
`, req.Description)
	}

	// Build the prompt
	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan soal ujian berdasarkan informasi berikut:
%[1]s- Mata Pelajaran: "%[2]s"
- Jumlah Soal: %[3]d
- Jumlah Soal Pilihan Ganda: %[4]d
- Jumlah Soal Isian: %[5]d
- Jumlah Soal Skala: %[6]d%[7]s

SANGAT PENTING: Anda HARUS menghasilkan TEPAT %[3]d soal total, tidak boleh kurang atau lebih. Hitung dengan teliti!

Distribusi tipe soal yang harus dihasilkan:
- TEPAT %[4]d soal Pilihan Ganda (option): Soal dengan beberapa pilihan jawaban, hanya satu yang benar
- TEPAT %[5]d soal Isian (text): Soal yang membutuhkan jawaban teks singkat
- TEPAT %[6]d soal Skala (scale): Soal yang meminta penilaian pada skala 1-5

Verifikasi jumlah soal:
- Total soal: %[4]d + %[5]d + %[6]d = %[3]d

Untuk setiap soal, sertakan:
- Teks soal yang jelas
- Tipe soal (option, text, atau scale)
- Bobot soal (nilai antara 1-10, sesuai tingkat kesulitan)
- Untuk soal pilihan ganda: 4 opsi jawaban dengan penanda jawaban yang benar
- Untuk soal skala: Label minimum dan maksimum yang jelas

Soal harus sesuai dengan tingkat pendidikan dan mata pelajaran yang diberikan, dengan tingkat kesulitan yang sesuai dengan karakteristik yang diminta.

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON. JANGAN TAMBAHKAN KOMENTAR, PENJELASAN, ATAU TEKS APAPUN DI LUAR JSON:

{
  "questions": [
    {
	  "no": "Nomor Soal",
      "text": "Teks soal 1",
      "type": "option",
      "weight": 5,
      "option_detail": {
        "options": [
          { "text": "Opsi A", "correct": true },
          { "text": "Opsi B", "correct": false },
          { "text": "Opsi C", "correct": false },
          { "text": "Opsi D", "correct": false }
        ]
      }
    },
    {
	  "no": "Nomor Soal",
      "text": "Teks soal 2",
      "type": "text",
      "weight": 3
    },
    {
	  "no": "Nomor Soal",
      "text": "Teks soal 3",
      "type": "scale",
      "weight": 2,
      "scale_detail": {
        "min_label": "Label minimum",
        "max_label": "Label maksimum"
      }
    }
  ]
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.`,
		examInfoText,        // %[1]s
		req.SubjectName,     // %[2]s
		req.QuestionCount,   // %[3]d
		optionCount,         // %[4]d
		textCount,           // %[5]d
		scaleCount,          // %[6]d
		examDescriptionText, // %[7]s
	)

	// Generate content using the AI model
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate exam questions: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result struct {
		Questions []model.ExamQuestion `json:"questions"`
	}

	// Clean and parse the response
	cleanJSON := extractJSON(responseText)
	if err := json.Unmarshal([]byte(cleanJSON), &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Log warning if question count doesn't match
	if len(result.Questions) != req.QuestionCount {
		log.Printf("Warning: generated %d questions, expected %d. Continuing with generated questions.", len(result.Questions), req.QuestionCount)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &model.ExamQuestions{Questions: result.Questions}, nil
}

// GenerateSingleExamQuestion generates a single exam question using AI
func (m *AIManager) GenerateSingleExamQuestion(ctx context.Context, req model.SingleExamQuestionRequest) (*model.ExamQuestion, error) {
	return m.generateSingleExamQuestion(ctx, req)
}

// generateSingleExamQuestion is an internal method to generate a single exam question
func (m *AIManager) generateSingleExamQuestion(ctx context.Context, req model.SingleExamQuestionRequest) (*model.ExamQuestion, error) {
	// Build exam context
	examContext := ""
	if req.Name != "" || req.ClassName != "" || req.Description != "" {
		parts := []string{}
		if req.Name != "" {
			parts = append(parts, fmt.Sprintf("ujian '%s'", req.Name))
		}
		if req.ClassName != "" {
			parts = append(parts, fmt.Sprintf("kelas '%s'", req.ClassName))
		}
		if req.Description != "" {
			parts = append(parts, fmt.Sprintf("dengan deskripsi '%s'", req.Description))
		}
		examContext = fmt.Sprintf(" untuk %s", joinWithAnd(parts))
	}

	// Build customization context
	customizationContext := ""
	if req.Customization != "" {
		customizationContext = fmt.Sprintf("\n\nInstruksi tambahan: %s", req.Customization)
	}

	var prompt string
	switch req.QuestionType {
	case "option":
		prompt = fmt.Sprintf(`
Buatlah satu soal pilihan ganda berkualitas tinggi untuk mata pelajaran %s%s.%s

Soal harus:
1. Relevan dengan mata pelajaran dan sesuai dengan tingkat pendidikan
2. Memiliki empat pilihan jawaban (A, B, C, D)
3. Memiliki satu jawaban yang benar
4. Memiliki pilihan pengecoh yang masuk akal
5. Jelas dan tidak ambigu
6. Jangan menggunakan katex/latex atau markdown. pakai teks yang berformat biasa saja dan bersih

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "text": "Teks pertanyaan lengkap",
  "type": "option",
  "weight": 5,
  "option_detail": {
    "options": [
      {"text": "Teks pilihan A", "correct": true/false},
      {"text": "Teks pilihan B", "correct": true/false},
      {"text": "Teks pilihan C", "correct": true/false},
      {"text": "Teks pilihan D", "correct": true/false}
    ]
  }
}

Pastikan hanya satu opsi yang memiliki "correct": true.
Pastikan semua konten dalam bahasa Indonesia yang baik dan benar.
Pastikan JSON yang dihasilkan valid dan dapat di-parse.`, req.SubjectName, examContext, customizationContext)

	case "text":
		prompt = fmt.Sprintf(`
Buatlah satu soal isian/esai berkualitas tinggi untuk mata pelajaran %s%s.%s

Soal harus:
1. Relevan dengan mata pelajaran dan sesuai dengan tingkat pendidikan
2. Memerlukan jawaban singkat atau esai pendek
3. Jelas dan tidak ambigu
4. Dapat dinilai secara objektif
5. Jangan menggunakan katex/latex atau markdown. pakai teks yang berformat biasa saja dan bersih

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "text": "Teks pertanyaan lengkap",
  "type": "text",
  "weight": 5
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar.
Pastikan JSON yang dihasilkan valid dan dapat di-parse.`, req.SubjectName, examContext, customizationContext)

	case "scale":
		prompt = fmt.Sprintf(`
Buatlah satu soal skala berkualitas tinggi untuk mata pelajaran %s dengan ketentuan adalah %s.%s

Soal harus:
1. Relevan dengan mata pelajaran dan sesuai dengan tingkat pendidikan
2. Meminta siswa untuk menilai sesuatu pada skala (misalnya 1-5)
3. Memiliki label yang jelas untuk nilai minimum dan maksimum
4. Jelas dan tidak ambigu
5. Jangan menggunakan katex/latex atau markdown. pakai teks yang berformat biasa saja dan bersih

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "text": "Teks pertanyaan lengkap",
  "type": "scale",
  "weight": 5,
  "scale_detail": {
    "min_label": "Label untuk nilai minimum (misalnya 'Sangat Tidak Setuju')",
    "max_label": "Label untuk nilai maksimum (misalnya 'Sangat Setuju')"
  }
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar.
Pastikan JSON yang dihasilkan valid dan dapat di-parse.`, req.SubjectName, examContext, customizationContext)

	default:
		return nil, fmt.Errorf("unsupported question type: %s", req.QuestionType)
	}

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result model.ExamQuestion
	if err := parseJSONResponse(responseText, &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &result, nil
}

// GenerateTeachingMaterials generates teaching materials using AI
func (m *AIManager) GenerateTeachingMaterials(ctx context.Context, req model.TeachingMaterialRequest) (*model.TeachingMaterial, error) {
	// Add specifications to the prompt if provided
	specificationText := ""
	if req.Specifications != "" {
		specificationText = fmt.Sprintf("\nSpesifikasi tambahan: %s", req.Specifications)
	}

	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan materi pembelajaran untuk mata pelajaran %s dengan judul "%s".%s

Bahan ajar ini akan digunakan sebagai materi pembelajaran untuk siswa dan akan dikonversi menjadi PDF.

Hasilkan bahan ajar dengan karakteristik berikut:
1. Judul yang jelas dan deskriptif
2. Konten lengkap dalam format Markdown yang terstruktur dengan baik
3. Minimal 7000 token (sekitar 5000-6000 kata) untuk memastikan bahan ajar cukup komprehensif
4. Gunakan heading (# untuk judul utama, ## untuk sub-judul, ### untuk sub-sub-judul), bullet points, dan format lainnya untuk membuat bahan ajar mudah dibaca
5. Sertakan:
   - Penjelasan konsep-konsep penting
   - Contoh-contoh yang relevan
   - Ilustrasi atau diagram yang dijelaskan secara tekstual
   - Latihan atau aktivitas untuk siswa
   - Rangkuman atau kesimpulan
   - Referensi atau sumber bacaan tambahan

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur berikut, tanpa teks tambahan di luar JSON:

{
  "title": "Judul materi pembelajaran",
  "content": "Konten materi pembelajaran lengkap dalam format Markdown"
}

Pastikan semua konten dalam bahasa Indonesia yang baik dan benar, serta sesuai dengan standar pendidikan Indonesia.
Pastikan JSON yang dihasilkan valid dan dapat di-parse. Jangan gunakan format lain selain JSON.
Jangan sertakan backtick atau tanda lain di awal atau akhir JSON.`, req.Subject, req.Title, specificationText)

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model")
	}

	// Parse JSON response
	var result model.TeachingMaterial
	if err := parseJSONResponse(responseText, &result); err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return &result, nil
}

// EditTeachingPlanSection edits a section of a teaching plan using AI
func (m *AIManager) EditTeachingPlanSection(ctx context.Context, req model.EditTeachingPlanSectionRequest) (string, error) {
	// Get section name based on section type
	sectionName := getSectionName(req.SectionType)

	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, bantu edit bagian "%s" dari rencana pembelajaran.

Konten saat ini:
%s

Instruksi untuk pengeditan:
%s

Hasilkan konten baru untuk bagian "%s" yang sudah diedit sesuai instruksi di atas.

SANGAT PENTING: Berikan respons HANYA berupa konten yang sudah diedit beserta format MARKDOWN yang sesuai. JANGAN sertakan teks tambahan apa pun. JANGAN gunakan format JSON. JANGAN gunakan backtick atau tanda kutip. Berikan respons berupa teks biasa dan bersih.`, sectionName, req.CurrentContent, req.Instructions, sectionName)

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return "", fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return "", fmt.Errorf("empty response from AI model")
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return responseText, nil
}

// GenerateTeachingPlanSection generates a specific section of a teaching plan using AI
func (m *AIManager) GenerateTeachingPlanSection(ctx context.Context, req model.GenerateTeachingPlanSectionRequest) (string, error) {

	// Add specifications to the prompt if provided
	specificationText := ""
	if req.Specifications != "" {
		specificationText = fmt.Sprintf("\nSpesifikasi tambahan: %s", req.Specifications)
	}

	// Add context information if provided
	contextText := ""
	if req.Title != "" {
		contextText = fmt.Sprintf("\nJudul rencana pembelajaran: \"%s\"", req.Title)
	}
	if req.Subject != "" {
		contextText += fmt.Sprintf("\nMata pelajaran: \"%s\"", req.Subject)
	}
	if req.ClassName != "" {
		contextText += fmt.Sprintf("\nKelas: \"%s\"", req.ClassName)
	}

	var prompt string
	switch req.SectionType {
	case "description":
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan bagian "Deskripsi" untuk rencana pembelajaran.%s%s

Buatkan deskripsi rencana pembelajaran yang:
1. Menjelaskan secara detail tentang rencana pembelajaran ini
2. Mencakup tujuan umum pembelajaran
3. Menjelaskan manfaat yang akan diperoleh siswa
4. Memberikan gambaran singkat tentang apa yang akan dipelajari
5. Sesuai dengan konteks pendidikan Indonesia

SANGAT PENTING: Berikan respons HANYA berupa konten deskripsi dalam format MARKDOWN yang sesuai tanpa perlu memberikan header awal. JANGAN sertakan teks tambahan apa pun. JANGAN gunakan format JSON. JANGAN gunakan backtick atau tanda kutip. Berikan respons berupa teks biasa dan bersih.`, contextText, specificationText)

	case "teachingGoal":
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan bagian "Tujuan Pembelajaran" untuk rencana pembelajaran.%s%s

Buatkan tujuan pembelajaran yang:
1. Menjelaskan tujuan pembelajaran spesifik yang ingin dicapai
2. Mencakup kompetensi yang akan dikembangkan
3. Menjelaskan hasil yang diharapkan setelah pembelajaran
4. Menggunakan kata kerja operasional yang terukur
5. Sesuai dengan standar pendidikan Indonesia

SANGAT PENTING: Berikan respons HANYA berupa konten tujuan pembelajaran dalam format MARKDOWN yang sesuai tanpa perlu memberikan header awal. JANGAN sertakan teks tambahan apa pun. JANGAN gunakan format JSON. JANGAN gunakan backtick atau tanda kutip. Berikan respons berupa teks biasa dan bersih.`, contextText, specificationText)

	case "teachingActivity":
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan bagian "Kegiatan Pembelajaran" untuk rencana pembelajaran.%s%s

Buatkan kegiatan pembelajaran yang:
1. Menjelaskan langkah-langkah kegiatan pembelajaran secara terstruktur
2. Mencakup aktivitas pembuka, inti, dan penutup
3. Menyertakan metode pengajaran yang sesuai
4. Menjelaskan alokasi waktu untuk setiap kegiatan
5. Mencakup interaksi yang diharapkan antara guru dan siswa
6. Sesuai dengan standar pendidikan Indonesia

SANGAT PENTING: Berikan respons HANYA berupa konten kegiatan pembelajaran dalam format MARKDOWN yang sesuai tanpa perlu memberikan header awal. JANGAN sertakan teks tambahan apa pun. JANGAN gunakan format JSON. JANGAN gunakan backtick atau tanda kutip. Berikan respons berupa teks biasa dan bersih.`, contextText, specificationText)

	case "teachingScoring":
		prompt = fmt.Sprintf(`
Sebagai asisten AI untuk guru, buatkan bagian "Penilaian" untuk rencana pembelajaran.%s%s

Buatkan metode penilaian yang:
1. Menjelaskan metode penilaian yang akan digunakan
2. Mencakup kriteria keberhasilan yang jelas
3. Menjelaskan cara mengukur pencapaian tujuan pembelajaran
4. Menyertakan berbagai jenis penilaian (formatif, sumatif, dll)
5. Mencakup rubrik atau pedoman penilaian

SANGAT PENTING: Berikan respons HANYA berupa konten penilaian dalam format MARKDOWN yang sesuai tanpa perlu memberikan header awal. JANGAN sertakan teks tambahan apa pun. JANGAN gunakan format JSON. JANGAN gunakan backtick atau tanda kutip. Berikan respons berupa teks biasa dan bersih.`, contextText, specificationText)

	default:
		return "", fmt.Errorf("unsupported section type: %s", req.SectionType)
	}

	// Generate content using the updated API
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return "", fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response text
	responseText := resp.Text()
	if responseText == "" {
		return "", fmt.Errorf("empty response from AI model")
	}

	// Testing for logging usage metadata (Token)
	usageMetadata, err := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	if err != nil {
		log.Fatal(err)
	}
	log.Printf("Usage Metadata: %s", string(usageMetadata))

	return responseText, nil
}

// EditExamQuestion edits an exam question using AI
func (m *AIManager) EditExamQuestion(ctx context.Context, req model.EditExamQuestionRequest) (*model.ExamQuestion, error) {
	// Serialize current question to JSON to include in the prompt
	currentQuestionJSON, err := json.MarshalIndent(req.CurrentQuestion, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to serialize current question: %w", err)
	}

	// Build exam context
	examContextParts := []string{}
	if req.ExamName != "" {
		examContextParts = append(examContextParts, fmt.Sprintf("ujian '%s'", req.ExamName))
	}
	if req.ClassName != "" {
		examContextParts = append(examContextParts, fmt.Sprintf("kelas '%s'", req.ClassName))
	}
	if req.SubjectName != "" {
		examContextParts = append(examContextParts, fmt.Sprintf("mata pelajaran '%s'", req.SubjectName))
	}
	if req.ExamDescription != "" {
		examContextParts = append(examContextParts, fmt.Sprintf("dengan deskripsi '%s'", req.ExamDescription))
	}

	examContext := ""
	if len(examContextParts) > 0 {
		examContext = fmt.Sprintf(" Konteks ujian: %s.", strings.Join(examContextParts, ", "))
	}

	prompt := fmt.Sprintf(`
Sebagai asisten AI untuk guru, bantu edit soal ujian berikut.
Soal saat ini adalah:
%s

Instruksi untuk pengeditan:
%s
%s

SANGAT PENTING: Berikan respons HANYA dalam format JSON dengan struktur yang SAMA PERSIS seperti soal asli, tanpa teks tambahan di luar JSON.
Struktur JSON yang diharapkan (sesuaikan nilai "type" dan detailnya jika tipe soal berubah):
{
	 "text": "Teks soal yang sudah diedit",
	 "type": "%s", // "option", "text", atau "scale"
	 "weight": %d, // Bobot soal (integer)
	 "option_detail": { // Hanya ada jika type adalah "option"
	   "options": [
	     {"text": "Teks pilihan A", "correct": true/false},
	     {"text": "Teks pilihan B", "correct": true/false},
	     {"text": "Teks pilihan C", "correct": true/false},
	     {"text": "Teks pilihan D", "correct": true/false}
	   ]
	 },
	 "scale_detail": { // Hanya ada jika type adalah "scale"
	   "min_label": "Label minimum yang sudah diedit",
	   "max_label": "Label maksimum yang sudah diedit"
	 }
}
Pastikan konten dalam soal ini jika ada equation tidak perlu menggunakan format katex agar bisa langsung terbaca
Pastikan semua konten dalam bahasa Indonesia yang baik dan benar.
Pastikan JSON yang dihasilkan valid dan dapat di-parse.
Jika instruksi meminta perubahan tipe soal, pastikan struktur JSON output sesuai dengan tipe soal yang baru.
Misalnya, jika soal diubah dari "option" menjadi "text", maka "option_detail" harus dihilangkan.
Jika soal diubah menjadi "option", pastikan "option_detail" dan minimal 2 opsi disertakan, dan salah satu opsi "correct": true.
Jika soal diubah menjadi "scale", pastikan "scale_detail" disertakan.
Pertahankan "weight" jika tidak ada instruksi untuk mengubahnya.
`, string(currentQuestionJSON), req.Instructions, examContext, req.CurrentQuestion.Type, req.CurrentQuestion.Weight)

	// Generate content using the AI model
	resp, err := m.client.Models.GenerateContent(
		ctx,
		m.model,
		genai.Text(prompt),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content for editing exam question: %w", err)
	}

	responseText := resp.Text()
	if responseText == "" {
		return nil, fmt.Errorf("empty response from AI model when editing exam question")
	}

	var editedQuestion model.ExamQuestion
	if err := parseJSONResponse(responseText, &editedQuestion); err != nil {
		return nil, fmt.Errorf("failed to parse AI response for edited exam question: %w. Response: %s", err, responseText)
	}

	// Log usage metadata
	usageMetadata, _ := json.MarshalIndent(resp.UsageMetadata, "", "  ")
	log.Printf("Usage Metadata (EditExamQuestion): %s", string(usageMetadata))

	return &editedQuestion, nil
}

/*
HELPER FUNCTION
*/

// parseJSONResponse parses a JSON response from the AI model
func parseJSONResponse(responseText string, result interface{}) error {
	// Extract JSON from the response if it's wrapped in code blocks
	jsonText := extractJSON(responseText)
	if jsonText == "" {
		jsonText = responseText
	}

	// Parse the JSON
	if err := json.Unmarshal([]byte(jsonText), result); err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	return nil
}

// extractJSON extracts JSON from a text that might contain code blocks
func extractJSON(text string) string {
	// Try to find JSON in code blocks
	jsonRegex := regexp.MustCompile("```(?:json)?\n((?:.|\n)*?)\n```")
	matches := jsonRegex.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}

	// Try to find JSON between curly braces
	braceRegex := regexp.MustCompile("{(?:.|\n)*}")
	matches = braceRegex.FindStringSubmatch(text)
	if len(matches) > 0 {
		return matches[0]
	}

	return ""
}

// containsAny checks if a string contains any of the given substrings
func containsAny(s string, substrings []string) bool {
	for _, sub := range substrings {
		if strings.Contains(strings.ToLower(s), strings.ToLower(sub)) {
			return true
		}
	}
	return false
}

// buildContextInfo builds context information for prompts
func buildContextInfo(req model.TaskRequest) string {
	contextInfo := ""
	if req.PlanTitle != "" || req.SubjectName != "" || req.ClassName != "" {
		var contextParts []string

		if req.PlanTitle != "" {
			contextParts = append(contextParts, fmt.Sprintf("rencana pembelajaran \"%s\"", req.PlanTitle))
		}

		if req.SubjectName != "" {
			contextParts = append(contextParts, fmt.Sprintf("mata pelajaran \"%s\"", req.SubjectName))
		}

		if req.ClassName != "" {
			contextParts = append(contextParts, fmt.Sprintf("kelas \"%s\"", req.ClassName))
		}

		if len(contextParts) > 0 {
			contextInfo = fmt.Sprintf("\nKonteks: Tugas ini adalah bagian dari %s.", joinWithFor(contextParts))
		}
	}
	return contextInfo
}

// joinWithFor joins strings with " untuk " between them
func joinWithFor(parts []string) string {
	if len(parts) == 0 {
		return ""
	}
	if len(parts) == 1 {
		return parts[0]
	}
	return strings.Join(parts[:len(parts)-1], " untuk ") + " untuk " + parts[len(parts)-1]
}

// joinWithAnd joins strings with " dan " between them
func joinWithAnd(parts []string) string {
	if len(parts) == 0 {
		return ""
	}
	if len(parts) == 1 {
		return parts[0]
	}
	return strings.Join(parts[:len(parts)-1], ", ") + " dan " + parts[len(parts)-1]
}

// getSectionName returns a human-readable name for a section type
func getSectionName(sectionType string) string {
	switch sectionType {
	case "title":
		return "Judul"
	case "description":
		return "Deskripsi"
	case "teachingGoal":
		return "Tujuan Pembelajaran"
	case "teachingActivity":
		return "Kegiatan Pembelajaran"
	case "teachingScoring":
		return "Penilaian"
	case "tasks":
		return "Tugas"
	case "teachingMaterials":
		return "Materi Pembelajaran"
	default:
		return sectionType
	}
}
